# 个人健康管理系统设计文档

## 1. 系统概述

## 2. 系统设计

### 2.1 系统架构设计

#### 2.1.1 总体架构

#### 2.1.2 功能说明

本系统采用四层架构设计，从用户角度提供完整的健康管理功能。以下是各功能层次的详细说明：

##### 第一层：用户界面层（User Interface Layer）

**1. 主菜单系统**
- **功能描述**：系统启动后为用户提供清晰的功能导航界面
- **用户体验**：
  - 采用美观的边框装饰设计，提供专业的视觉体验
  - 支持中文界面，操作简单直观
  - 提供6个主要功能选项：健康信息录入、健康数据分析、慢性病评估、数据查询、数据管理、退出系统
  - 具备输入验证和错误处理功能，确保用户操作的准确性

**2. 交互界面系统**
- **功能描述**：为用户提供友好的交互体验和操作引导
- **用户体验**：
  - 智能输入提示和数据验证，帮助用户正确输入健康数据
  - 逐步引导式操作流程，降低使用难度
  - 提供确认对话和选择提示，避免误操作
  - 友好的错误信息显示，帮助用户快速定位和解决问题

**3. 结果展示系统**
- **功能描述**：以专业、清晰的方式展示分析结果和健康报告
- **用户体验**：
  - 格式化的健康报告显示，信息层次分明
  - 关键信息突出显示，便于用户快速获取重要信息
  - 支持分级展示（简要→详细），满足不同用户的信息需求
  - 图表化的趋势展示，直观反映健康变化

##### 第二层：业务功能层（Business Logic Layer）

**1. 健康信息录入功能**
- **用户操作**：
  - 手动录入19个健康指标，包括基本信息（日期、姓名、年龄、性别、身高、体重）
  - 输入生理指标（血压、血糖、血脂、肌酐、心率等）
  - 记录生活习惯（睡眠时长、吸烟状况、家族史）
  - 录入特殊指标（脂蛋白A、超敏C反应蛋白）
- **用户价值**：建立完整的个人健康档案，为后续健康分析和管理提供数据基础

**2. 健康数据分析功能**
- **多维度健康评估**：
  - BMI评估：自动计算体重指数并提供分类（偏瘦/正常/超重/肥胖）
  - 血压评估：基于最新医学标准进行血压分级（正常/高血压前期/1-3级高血压）
  - 血糖评估：评估糖尿病风险（正常/糖尿病前期/糖尿病）
  - 血脂评估：分析心血管疾病风险
  - 肾功能评估：基于肌酐值评估肾功能状态
  - 生活方式评估：评价睡眠、运动、吸烟等生活习惯对健康的影响
  - 炎症指标评估：基于超敏C反应蛋白评估炎症状态
- **综合健康评分**：
  - 各维度加权计算，生成总体健康分数
  - 提供健康等级评定（优秀/良好/一般/较差/差）
  - 生成个性化健康改善建议
- **用户价值**：全面了解自身健康状况，获得专业的健康指导和改善建议

**3. 慢性病风险评估功能**
- **心脑血管疾病风险评估**：
  - 基于China-PAR模型进行10年心血管病风险预测
  - 综合考虑年龄、性别、血压、血脂、吸烟、家族史等多个风险因素
  - 提供风险分级：低危(<5%)/中危(5-9%)/高危(10-19%)/极高危(≥20%)
- **糖尿病风险评估**：
  - 基于血糖、糖化血红蛋白、BMI等指标评估2型糖尿病发病风险
  - 提供预防建议和监测频率建议
- **慢性肾病风险评估**：
  - 基于肌酐计算肾小球滤过率(eGFR)
  - 进行CKD分期评估（1-5期）
  - 提供肾功能下降风险预警
- **肿瘤风险预警**：
  - 基于炎症指标、生活习惯等因素进行风险评估
  - 提供肿瘤筛查建议和高危人群识别
- **用户价值**：早期发现慢性病风险，制定科学的预防策略，降低疾病发生率

**4. 数据查询功能**
- **多维度查询方式**：
  - 按日期查询：精确查找特定日期的健康记录
  - 按姓名查询：支持模糊匹配的姓名搜索功能
  - 按指标范围查询：根据血压、血糖、心率等指标范围进行筛选
  - 显示全部记录：浏览所有已存储的健康数据
- **查询结果处理**：
  - 智能过滤无效数据，确保查询结果的准确性
  - 支持简要列表和详细信息两种显示模式
  - 提供交互式记录选择和查看功能
- **用户价值**：快速检索历史健康数据，追踪健康变化趋势，为健康管理决策提供数据支持

**5. 数据管理功能**
- **数据导入功能**：
  - 从CSV文件批量导入健康记录，支持历史数据迁移
  - 支持按日期过滤导入，灵活控制数据范围
  - 自动去重和数据验证，确保数据质量
  - 提供错误记录统计和详细报告
- **数据导出功能**：
  - 将健康记录导出为标准CSV格式，便于数据共享和备份
  - 自动生成时间戳文件名，避免文件覆盖
  - 支持UTF-8编码，确保中文字符正确显示
  - 增量保存机制，避免重复导出相同数据
- **数据展示功能**：
  - 格式化显示已加载的健康数据
  - 自动计算衍生指标（如BMI等）
  - 提供数据统计信息和概览
- **用户价值**：提供便捷的数据备份、迁移和共享功能，确保健康数据的安全性和可用性

##### 第三层：数据处理层（Data Processing Layer）

**1. 数据转换处理模块**
- **处理功能**：
  - 不同数据结构间的无缝转换
  - 数据格式标准化处理
  - 单位换算和数值标准化
  - 数据完整性检查和修复
- **用户受益**：确保数据在系统内部的一致性和准确性

**2. 健康评估算法模块**
- **算法功能**：
  - BMI计算和分类算法：基于WHO标准进行体重分类
  - 血压分级算法：遵循中国高血压防治指南标准
  - 血糖评估算法：基于ADA和中国糖尿病防治指南
  - 血脂风险评估算法：参考中国成人血脂异常防治指南
  - 肾功能评估算法：使用CKD-EPI公式计算eGFR
  - 综合评分算法：多维度加权评估整体健康状况
- **用户受益**：获得基于权威医学标准的专业健康评估

**3. 风险分析算法模块**
- **算法功能**：
  - China-PAR心血管风险评估模型：适合中国人群的风险预测
  - 糖尿病风险预测算法：基于多因素回归模型
  - 慢性肾病风险分层算法：结合eGFR和蛋白尿评估
  - 肿瘤风险预警算法：基于生活方式和生物标志物
  - eGFR计算算法：精确评估肾功能状态
- **用户受益**：获得科学准确的疾病风险预测，指导预防措施

**4. 趋势分析算法模块**
- **时间序列分析**：
  - 健康指标变化趋势计算，识别上升、下降或稳定趋势
  - 变化率和变化幅度分析，量化健康变化程度
  - 智能时间间隔处理，适应不同的监测频率
- **预警系统**：
  - 三级预警机制（正常/关注/警告），分级管理健康风险
  - 基于医学标准的阈值设定，确保预警的科学性
  - 个性化预警建议生成，提供针对性的健康指导
- **慢性病趋势分析**：
  - 心血管疾病风险趋势追踪
  - 糖尿病进展趋势监测
  - 肾功能变化趋势分析
  - 炎症指标趋势评估
- **用户受益**：及时发现健康问题的早期信号，实现主动健康管理

##### 第四层：数据存储层（Data Storage Layer）

**1. 文件导入模块**
- **存储功能**：
  - CSV文件解析和读取，支持标准格式数据导入
  - 批量数据加载，提高数据处理效率
  - 文件格式验证，确保数据完整性
  - 完善的错误处理和数据恢复机制
- **用户受益**：可靠的数据导入功能，支持历史数据迁移

**2. 文件导出模块**
- **存储功能**：
  - CSV格式数据写入，确保数据的标准化输出
  - 智能文件命名和路径管理
  - 数据完整性保证机制
  - 并发访问控制，防止数据冲突
- **用户受益**：安全可靠的数据导出和备份功能

**3. 数据持久化**
- **存储特性**：
  - 19个健康指标字段的完整存储
  - 内存数组和文件系统的双重存储保障
  - 数据一致性维护机制
  - 自动备份和恢复功能
- **用户受益**：确保健康数据的长期保存和安全性

#### 2.1.3 系统数据流向

系统数据流向遵循"输入→验证→存储→分析→展示"的标准流程：

1. **数据输入阶段**：用户通过界面录入健康数据或导入CSV文件
2. **数据验证阶段**：系统自动验证数据的完整性、合理性和一致性
3. **数据存储阶段**：验证通过的数据存储到内存数组和文件系统
4. **数据分析阶段**：调用相应的算法模块进行健康评估、风险分析或趋势分析
5. **结果展示阶段**：将分析结果以用户友好的方式展示给用户

这种设计确保了数据处理的准确性、系统运行的稳定性和用户体验的流畅性。

#### 2.1.4 用户使用场景

**场景1：日常健康监测**
- 用户定期录入健康数据（血压、血糖等）
- 查看健康评估报告和个性化建议
- 追踪健康指标的变化趋势
- 接收健康预警信息

**场景2：慢性病风险管理**
- 完整录入健康数据进行风险评估
- 查看各类慢性病的风险分级
- 获得针对性的预防建议
- 定期监测风险变化趋势

**场景3：健康数据管理**
- 导入历史健康数据建立完整档案
- 查询特定时期的健康记录
- 导出数据进行备份或与医生分享
- 管理家庭成员的健康数据

**场景4：长期健康追踪**
- 持续录入健康数据建立时间序列
- 分析长期健康趋势和变化模式
- 评估健康干预措施的效果
- 制定个性化的健康管理计划

通过这四层架构设计，系统为用户提供了专业、智能、便捷的个人健康管理解决方案，帮助用户实现科学的健康管理和疾病预防。